// src/app/api/chat/route.ts

import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { streamText, convertToModelMessages } from 'ai';

// Permite que a Vercel execute isso em uma infraestrutura mais rápida
export const runtime = 'edge';

// Cria uma instância do provedor do Google Generative AI
const google = createGoogleGenerativeAI({
  // A chave da API é lida automaticamente de process.env.GOOGLE_API_KEY
});

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Convert UIMessages to ModelMessages for AI SDK v5
    const modelMessages = convertToModelMessages(messages);

    // Peça ao AI SDK para fazer o streaming da resposta do modelo
    const result = await streamText({
      model: google('models/gemini-1.5-flash-latest'),
      messages: modelMessages,
    });

    // Converte o resultado em uma StreamingTextResponse e a retorna
    return result.toTextStreamResponse();

  } catch (error) {
    console.error("Error in API route:", error);
    // Retorna uma resposta de erro em formato JSON
    return new Response(JSON.stringify({ error: 'Ocorreu um erro no servidor.' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}