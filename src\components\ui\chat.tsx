"use client"

import {
  PromptInput,
  PromptInputAction,
  PromptInputActions,
  PromptInputTextarea,
} from "@/components/ui/prompt-input"
import { Button } from "@/components/ui/button"
import { Message, MessageAvatar, MessageContent } from "@/components/ui/message"
import {
  ChatContainerRoot,
  ChatContainerContent,
  ChatContainerScrollAnchor,
} from "@/components/ui/chat-container"
import { ArrowUp, Plus } from "lucide-react"
import type React from "react"
import type { UIMessage } from "ai"
import { useState } from "react"

interface ChatProps {
  messages: UIMessage[]
  sendMessage: (message: { text: string }) => Promise<void>
  isLoading: boolean
  user?: { name: string }
  bot?: { name: string }
}

// Helper function to extract text content from UIMessage parts
function getMessageText(message: UIMessage): string {
  return message.parts
    .filter((part) => part.type === 'text')
    .map((part) => (part as any).text)
    .join('')
}

function Chat({
  messages,
  sendMessage,
  isLoading,
  user = { name: "You" },
  bot = { name: "AI" },
}: ChatProps) {
  const [input, setInput] = useState("")

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return
    sendMessage({ text: input })
    setInput("")
  }

  const handlePromptSubmit = () => {
    if (!input.trim() || isLoading) return
    sendMessage({ text: input })
    setInput("")
  }

  return (
    <div className="flex h-full flex-col">
      {/* Messages Container */}
      <ChatContainerRoot className="flex-1 px-4">
        <ChatContainerContent>
          {messages.length === 0 ? (
            <div className="flex h-full items-center justify-center">
              <div className="text-center">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Welcome to {bot.name}
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Start a conversation by typing a message below.
                </p>
              </div>
            </div>
          ) : (
            <>
              {messages.map((message) => (
                <Message key={message.id}>
                  <MessageAvatar
                    src=""
                    alt={message.role === "user" ? user.name : bot.name}
                    fallback={message.role === "user" ? user.name[0] : bot.name[0]}
                  />
                  <MessageContent markdown>
                    {getMessageText(message)}
                  </MessageContent>
                </Message>
              ))}
              <ChatContainerScrollAnchor />
            </>
          )}
        </ChatContainerContent>
      </ChatContainerRoot>

      {/* Input Container */}
      <div className="border-t bg-background p-4">
        <form onSubmit={handleFormSubmit}>
          <PromptInput
            isLoading={isLoading}
            value={input}
            onValueChange={setInput}
            onSubmit={handlePromptSubmit}
            className="border-input bg-background relative z-10 w-full rounded-3xl border p-0 pt-1 shadow-sm"
          >
            <div className="flex flex-col">
              <PromptInputTextarea
                placeholder="Type your message..."
                className="min-h-[44px] pt-3 pl-4 text-base leading-[1.3] sm:text-base md:text-base"
              />

              <PromptInputActions className="mt-2 flex w-full items-center justify-between gap-2 px-3 pb-3">
                <div className="flex items-center gap-2">
                  <PromptInputAction tooltip="Add attachment">
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="size-8 rounded-full"
                    >
                      <Plus size={16} />
                    </Button>
                  </PromptInputAction>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    type="submit"
                    size="icon"
                    disabled={!input.trim() || isLoading}
                    className="size-8 rounded-full"
                  >
                    {!isLoading ? (
                      <ArrowUp size={16} />
                    ) : (
                      <div className="size-3 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    )}
                  </Button>
                </div>
              </PromptInputActions>
            </div>
          </PromptInput>
        </form>
      </div>
    </div>
  )
}

export { Chat }
