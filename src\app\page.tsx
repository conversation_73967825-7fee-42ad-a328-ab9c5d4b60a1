// app/page.tsx
'use client';

import { useChat } from 'ai/react';
import { Chat } from '@/components/ui/chat'; // Importando o componente do Prompt Kit
import { toast, Toaster } from 'react-hot-toast';
import { useEffect } from 'react';

export default function MyChatComponent() {
  const { messages, input, handleInputChange, handleSubmit, error, isLoading } =
    useChat({
      // A rota da API continua a mesma
      api: '/api/chat',
    });

  // Efeito para monitorar e exibir erros com toast
  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
      <Toaster position="top-center" />
      <div className="w-full max-w-2xl h-[85vh]">
        <Chat
          messages={messages}
          input={input}
          onInputChange={handleInputChange}
          onSubmit={handleSubmit}
          isLoading={isLoading}
          user={{ name: 'Você' }} // Opcional: define o nome do usuário
          bot={{ name: 'Gemini AI' }} // Opcional: define o nome do bot
        />
      </div>
    </div>
  );
}